const express = require('express');
const cors = require('cors');
const sql = require('mssql'); // 引入 mssql 包

const app = express();
const port = 3000;

app.use(cors());

// --- 数据库连接配置 (mssql 格式) ---
const dbConfig = {
    user: 'sa',
    password: '12345678',
    server: '**************', // 或 'localhost' 如果在同一台机器
    database: 'SenOnlineServer',
    options: {
        encrypt: false, // 如果是本地或信任的网络，可以设为 false；否则建议 true
        trustServerCertificate: true, // 如果 encrypt 为 true 且使用自签名证书，需要设为 true
        instanceName: 'ZCWIFIDATA' // 指定实例名
    },
    pool: { // 连接池配置
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000
    }
};

// --- 全局变量存储连接池 ---
let pool;

// --- 异步初始化函数 ---
async function initializeApp() {
    try {
        // 使用 mssql.connect() 创建并连接连接池
        pool = await sql.connect(dbConfig);
        console.log("Database connection pool initialized successfully using mssql.");

        // --- API 端点：获取所有工位数据 ---
        app.get('/api/workstations', async (req, res) => {
            // 检查 pool 是否已初始化
            if (!pool || !pool.connected) {
                console.error("Connection pool not initialized or not connected.");
                return res.status(500).json({ error: "Server not ready, connection pool unavailable." });
            }

            try {
                // 1. 从表1获取所有设备基础信息 (添加 DeviceStatus 和 IsFault)
                const deviceSql = `
                    SELECT DeviceName, Address, Version, LoginId, DeviceStatus, IsFault
                    FROM DeviceList
                `;
                const devicesResult = await pool.request().query(deviceSql);
                const devices = devicesResult.recordset;

                // 2. 为每个设备获取最新的 5 条状态日志记录
                const workstationsPromises = devices.map(async (device) => {
                    // a. 确定整体实时状态 based on IsFault
                    let overallStatusType = 'offline'; // Default
                    switch (String(device.IsFault).trim()) { // Compare as string
                        case '0':
                            overallStatusType = 'online';
                            break;
                        case '1': // Assuming '1' is error based on previous logic
                            overallStatusType = 'error';
                            break;
                        case '2': // Assuming '2' is offline
                            overallStatusType = 'offline';
                            break;
                        default:
                            console.warn(`Unknown device.IsFault value: '${device.IsFault}'`);
                            break;
                    }

                    // b. 主接口不再查询日志数据，提高性能
                    // 日志数据将通过专门的 /api/workstations/:name/logs 接口按需获取

                    // 3. 组合数据，包含基本信息（不包含日志数据以提高性能）
                    return {
                        name: device.DeviceName,
                        location: device.Address,
                        overallStatusType: overallStatusType, // Pass the overall status type
                        realtimeStatusMessage: device.DeviceStatus || '无实时状态信息', // Pass the real-time status message
                        remoteId: device.LoginId,
                        // verificationCode: device.LoginPassword,
                        softwareVersion: device.Version
                    };
                });

                // 等待所有设备的状态查询完成
                const workstations = await Promise.all(workstationsPromises);

                // 发送组合好的 JSON 数据
                res.json(workstations);

            } catch (error) {
                console.error("Error fetching data from database:", error);
                res.status(500).json({ error: "Internal server error while fetching data." });
            }
            // mssql 连接池通常不需要手动关闭连接
        });

        // --- API 端点：获取指定工位的详细日志数据 ---
        app.get('/api/workstations/:name/logs', async (req, res) => {
            // 检查 pool 是否已初始化
            if (!pool || !pool.connected) {
                console.error("Connection pool not initialized or not connected.");
                return res.status(500).json({ error: "Server not ready, connection pool unavailable." });
            }

            try {
                const deviceName = req.params.name;
                const limit = parseInt(req.query.limit) || 500; // 默认获取500条，可通过query参数调整
                const offset = parseInt(req.query.offset) || 0; // 偏移量，用于分页

                // 验证limit参数范围
                const maxLimit = Math.min(Math.max(limit, 10), 1000); // 限制在10-1000条之间

                console.log(`Fetching ${maxLimit} logs for device: ${deviceName}, offset: ${offset}`);
                console.log('SQL Query:', statusLogSql); // 调试SQL查询

                // 获取指定工位的详细日志（支持分页）- 使用ROW_NUMBER()方式兼容更多SQL Server版本
                const statusLogSql = `
                    WITH LogsWithRowNumber AS (
                        SELECT Time, DoLog, State,
                               ROW_NUMBER() OVER (ORDER BY Time DESC) as RowNum
                        FROM DoLog
                        WHERE DeviceName = @DeviceName
                    )
                    SELECT Time, DoLog, State
                    FROM LogsWithRowNumber
                    WHERE RowNum > @Offset AND RowNum <= (@Offset + @Limit)
                    ORDER BY Time DESC
                `;

                // 同时获取总记录数
                const countSql = `
                    SELECT COUNT(*) as TotalCount
                    FROM DoLog
                    WHERE DeviceName = @DeviceName
                `;

                // 执行两个查询：获取日志数据和总数
                const [statusLogResult, countResult] = await Promise.all([
                    pool.request()
                        .input('DeviceName', sql.NVarChar, deviceName)
                        .input('Offset', sql.Int, offset)
                        .input('Limit', sql.Int, maxLimit)
                        .query(statusLogSql),
                    pool.request()
                        .input('DeviceName', sql.NVarChar, deviceName)
                        .query(countSql)
                ]);

                const statusLogsData = statusLogResult.recordset;
                const totalCount = countResult.recordset[0]?.TotalCount || 0;

                if (statusLogsData && statusLogsData.length > 0) {
                    // 转换日志记录
                    const formattedStatusLogs = statusLogsData.map(log => {
                        let logStatusType = 'offline';
                        let logMessagePrefix = '离线-';
                        switch (String(log.State).trim()) {
                            case '0':
                                logStatusType = 'online';
                                logMessagePrefix = '在线-';
                                break;
                            case '1': // Error
                                logStatusType = 'error';
                                logMessagePrefix = '异常-';
                                break;
                            case '2': // Offline
                                logStatusType = 'offline';
                                logMessagePrefix = '离线-';
                                break;
                            default:
                                console.warn(`Unknown log.State value: '${log.State}'`);
                                break;
                        }
                        return {
                            timestamp: log.Time || 'N/A',
                            message: `${logMessagePrefix}${log.DoLog || '无日志信息'}`,
                            type: logStatusType
                        };
                    });

                    res.json({
                        deviceName: deviceName,
                        totalLogs: totalCount, // 使用实际的总记录数
                        currentBatch: formattedStatusLogs.length,
                        offset: offset,
                        logs: formattedStatusLogs
                    });
                } else {
                    res.json({
                        deviceName: deviceName,
                        totalLogs: totalCount, // 即使没有数据也返回总数
                        currentBatch: 0,
                        offset: offset,
                        logs: []
                    });
                }

            } catch (error) {
                console.error(`Error fetching logs for device ${req.params.name}:`, error);
                res.status(500).json({ error: "Internal server error while fetching logs." });
            }
        });

        // 启动服务器 (在连接池初始化成功后)
        app.listen(port, () => {
            console.log(`Backend server listening at http://localhost:${port}`);
        });

    } catch (err) {
        console.error("Failed to initialize database connection pool:", err);
        process.exit(1); // 初始化失败则退出应用
    }
}

// --- 启动应用初始化 ---
initializeApp();

// 添加优雅关闭连接池的逻辑 (可选但推荐)
process.on('SIGINT', async () => {
    console.log('Closing database connection pool...');
    if (pool) {
        await pool.close();
    }
    process.exit(0);
});


// const express = require('express');
// const cors = require('cors');
// const sql = require('mssql'); // 引入 mssql 包
// const jwt = require('jsonwebtoken'); // 引入jsonwebtoken
// const bcrypt = require('bcryptjs'); // 引入bcryptjs

// const app = express();
// const port = 3000;

// app.use(cors());
// app.use(express.json()); // <--- 新增：用于解析 POST 请求的 JSON body

// // --- 新增：认证配置 ---
// const FIXED_USERNAME = 'zhice'; // 设置固定用户名
// const FIXED_PASSWORD_HASH = '$2b$10$9ztf5bq6/zg9xJZn1a1WouCWmdCtHtJ.DR2bbAtaKXvk3XNIK8/O6'; // <--- 在这里粘贴你生成的哈希值
// const JWT_SECRET = 'a9f8d7e6c5b4a3f2e1d0c9b8a7f6e5d4'; // !!! 请务必修改为一个强随机密钥 !!!
// const TOKEN_EXPIRATION = '1h'; // Token 有效期，例如 1 小时

// // --- 新增：登录路由 ---
// app.post('/login', async (req, res) => {
//     const { username, password } = req.body;

//     if (!username || !password) {
//         return res.status(400).json({ message: 'Username and password are required.' });
//     }

//     if (username !== FIXED_USERNAME) {
//         return res.status(401).json({ message: 'Invalid credentials.' });
//     }

//     try {
//         const isMatch = await bcrypt.compare(password, FIXED_PASSWORD_HASH);
//         if (!isMatch) {
//             return res.status(401).json({ message: 'Invalid credentials.' });
//         }

//         // 凭证有效，生成 JWT
//         const token = jwt.sign(
//             { username: FIXED_USERNAME }, // Payload
//             JWT_SECRET,
//             { expiresIn: TOKEN_EXPIRATION } // 设置过期时间
//         );

//         res.json({ token }); // 返回 Token 给前端

//     } catch (error) {
//         console.error("Login error:", error);
//         res.status(500).json({ message: 'Internal server error during login.' });
//     }
// });

// // --- 新增：认证中间件 ---
// const authenticateToken = (req, res, next) => {
//     const authHeader = req.headers['authorization'];
//     const token = authHeader && authHeader.split(' ')[1]; // 从 "Bearer TOKEN" 中提取 TOKEN

//     if (token == null) {
//         return res.sendStatus(401); // 没有 token
//     }

//     jwt.verify(token, JWT_SECRET, (err, user) => {
//         if (err) {
//             console.error("JWT Verification Error:", err.message);
//             if (err.name === 'TokenExpiredError') {
//                  return res.status(401).json({ message: 'Token expired.' });
//             }
//             return res.sendStatus(403); // token 无效
//         }
//         req.user = user; // 将解码后的用户信息附加到请求对象
//         next(); // 继续
//     });
// };


// // --- 数据库连接配置 (mssql 格式) ---
// const dbConfig = {
//     user: 'sa',
//     password: '12345678',
//     server: '**************', // 或 'localhost' 如果在同一台机器
//     database: 'SenOnlineServer',
//     options: {
//         encrypt: false, // 如果是本地或信任的网络，可以设为 false；否则建议 true
//         trustServerCertificate: true, // 如果 encrypt 为 true 且使用自签名证书，需要设为 true
//         instanceName: 'ZCWIFIDATA' // 指定实例名
//     },
//     pool: { // 连接池配置
//         max: 10,
//         min: 0,
//         idleTimeoutMillis: 30000
//     }
// };

// // --- 全局变量存储连接池 ---
// let pool;

// // --- 异步初始化函数 ---
// async function initializeApp() {
//     try {
//         // 使用 mssql.connect() 创建并连接连接池
//         pool = await sql.connect(dbConfig);
//         console.log("Database connection pool initialized successfully using mssql.");

//         // --- API 端点：获取所有工位数据 ---
//         // vvv 新增：在这里应用认证中间件 vvv
//         app.get('/api/workstations', authenticateToken, async (req, res) => {
//             // 检查 pool 是否已初始化
//             if (!pool || !pool.connected) {
//                  console.error("Connection pool not initialized or not connected.");
//                  return res.status(500).json({ error: "Server not ready, connection pool unavailable." });
//             }

//             try {
//                 console.log(`User ${req.user.username} requested workstation data.`); // <--- 新增：可以记录访问日志
//                 // 1. 从表1获取所有设备基础信息 (添加 DeviceStatus 和 IsFault)
//                 const deviceSql = `
//                     SELECT DeviceName, Address, Version, LoginId, LoginPassword, DeviceStatus, IsFault
//                     FROM DeviceList
//                 `;
//                 const devicesResult = await pool.request().query(deviceSql);
//                 const devices = devicesResult.recordset;

//                 // 2. 为每个设备获取最新的 5 条状态日志记录
//                 const workstationsPromises = devices.map(async (device) => {
//                     // a. 确定整体实时状态 based on IsFault
//                     let overallStatusType = 'offline'; // Default
//                     let overallStatusPrefix = '离线-'; // Default prefix for consistency if needed, though DeviceStatus is separate
//                     switch (String(device.IsFault).trim()) { // Compare as string
//                         case '0':
//                             overallStatusType = 'online';
//                             overallStatusPrefix = '在线-';
//                             break;
//                         case '1': // Assuming '1' is error based on previous logic
//                             overallStatusType = 'error';
//                             overallStatusPrefix = '异常-';
//                             break;
//                         case '2': // Assuming '2' is offline
//                             overallStatusType = 'offline';
//                             overallStatusPrefix = '离线-';
//                             break;
//                         default:
//                             console.warn(`Unknown device.IsFault value: '${device.IsFault}'`);
//                             break;
//                     }

//                     // b. 获取运行日志 (DoLog)
//                     const statusLogSql = `
//                         SELECT TOP 5 Time, DoLog, State
//                         FROM DoLog
//                         WHERE DeviceName = @DeviceName -- 使用命名参数 @DeviceName
//                         ORDER BY Time DESC
//                     `;

//                     let statusLogsData = []; // Renamed from statusLogs
//                     let formattedStatusLogs = []; // Renamed from formattedStatus

//                     try {
//                         const statusLogResult = await pool.request()
//                             .input('DeviceName', sql.NVarChar, device.DeviceName)
//                             .query(statusLogSql);
//                         statusLogsData = statusLogResult.recordset;

//                         if (statusLogsData && statusLogsData.length > 0) {
//                             // 转换日志记录
//                             formattedStatusLogs = statusLogsData.map(log => {
//                                 let logStatusType = 'offline';
//                                 let logMessagePrefix = '离线-';
//                                 switch (String(log.State).trim()) { // Compare log state as string
//                                     case '0':
//                                         logStatusType = 'online';
//                                         logMessagePrefix = '在线-';
//                                         break;
//                                     case '1': // Error
//                                         logStatusType = 'error';
//                                         logMessagePrefix = '异常-';
//                                         break;
//                                     case '2': // Offline
//                                         logStatusType = 'offline';
//                                         logMessagePrefix = '离线-';
//                                         break;
//                                     default:
//                                          console.warn(`Unknown log.State value: '${log.State}'`);
//                                          break;
//                                 }
//                                 return {
//                                     timestamp: log.Time || 'N/A',
//                                     message: `${logMessagePrefix}${log.DoLog || '无日志信息'}`,
//                                     type: logStatusType
//                                 };
//                             });
//                         } else {
//                             console.log(`No logs found for '${device.DeviceName}'. StatusLogs array is empty or null.`);
//                             // No need to add a default log entry if none exist
//                         }

//                     } catch (queryError) {
//                         console.error(`Error querying DoLog for '${device.DeviceName}':`, queryError);
//                         // Optionally add an error log entry to formattedStatusLogs here
//                          formattedStatusLogs.push({
//                              timestamp: 'N/A',
//                              message: `查询运行日志出错: ${queryError.message}`,
//                              type: 'error'
//                          });
//                     }

//                     // 3. 组合数据，包含新的实时状态和日志
//                     return {
//                         name: device.DeviceName,
//                         location: device.Address,
//                         overallStatusType: overallStatusType, // Pass the overall status type
//                         realtimeStatusMessage: device.DeviceStatus || '无实时状态信息', // Pass the real-time status message
//                         statusLogs: formattedStatusLogs, // Pass the formatted logs
//                         remoteId: device.LoginId,
//                         verificationCode: device.LoginPassword,
//                         softwareVersion: device.Version
//                     };
//                 });

//                 // 等待所有设备的状态查询完成
//                 const workstations = await Promise.all(workstationsPromises);

//                 // 发送组合好的 JSON 数据
//                 res.json(workstations);

//             } catch (error) {
//                 console.error("Error fetching data from database:", error);
//                 res.status(500).json({ error: "Internal server error while fetching data." });
//             }
//             // mssql 连接池通常不需要手动关闭连接
//         });

//         // 启动服务器 (在连接池初始化成功后)
//         app.listen(port, () => {
//             console.log(`Backend server listening at http://localhost:${port}`);
//         });

//     } catch (err) {
//         console.error("Failed to initialize database connection pool:", err);
//         process.exit(1); // 初始化失败则退出应用
//     }
// }

// // --- 启动应用初始化 ---
// initializeApp();

// // 添加优雅关闭连接池的逻辑 (可选但推荐)
// process.on('SIGINT', async () => {
//     console.log('Closing database connection pool...');
//     if (pool) {
//         await pool.close();
//     }
//     process.exit(0);
// });
