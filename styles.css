:root {
    --primary-color: #1e88e5;
    --primary-light: rgba(30, 136, 229, 0.1);
    --success-color: #4caf50;
    --warning-color: #fb8c00;
    --danger-color: #e53935;
    --secondary-color: #909399;
    --info-color: #546e7a;
    --error-color: #f44336;
    --bg-color: #f5f7fa;
    --card-bg: #ffffff;
    --header-bg: rgba(255, 255, 255, 0.8);
    --header-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    --text-primary: #303133;
    --text-regular: #606266;
    --text-secondary: #909399;
    --border-color: #e4e7ed;
    --header-height: 60px;
    --border-radius: 12px;
    --card-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    --hover-transition: transform 0.2s ease, box-shadow 0.2s ease;
    --theme-transition: background-color 0.15s ease, color 0.15s ease, border-color 0.15s ease;
}

html.dark {
    --bg-color: #121212;
    --card-bg: #1e1e1e;
    --header-bg: rgba(30, 30, 30, 0.8);
    --header-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    --text-primary: #e0e0e0;
    --text-regular: #b0b0b0;
    --text-secondary: #909090;
    --border-color: #303030;
    --success-color: #66bb6a;
    --danger-color: #ef5350;
    --warning-color: #ffa726;
    --error-color: #ff6659;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

body {
    background-color: var(--bg-color);
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.5;
    transition: var(--theme-transition);
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

.container {
    width: 94%;
    max-width: 1400px;
    margin: 0 auto;
}

.app-header {
    height: var(--header-height);
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: transparent;
    transition: var(--theme-transition);
}

.app-header.scrolled {
    background-color: var(--header-bg);
    backdrop-filter: blur(10px);
    box-shadow: var(--header-shadow);
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo-icon {
    font-size: 24px;
    color: var(--primary-color);
}

.logo-text {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.time-display {
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
}

.header-options {
    display: flex;
    align-items: center;
    gap: 16px;
}

.header-option {
    font-size: 18px;
    color: var(--text-regular);
    cursor: pointer;
    position: relative;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-option:hover {
    color: var(--primary-color);
}

.badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--danger-color);
    color: white;
    font-size: 10px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.mobile-menu-btn {
    display: none;
}

.mobile-menu {
    position: absolute;
    top: var(--header-height);
    right: 10px;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    min-width: 200px;
    padding: 8px 0;
    z-index: 101;
    display: none;
    opacity: 0;
    transform: translateY(-10px);
    transition: var(--theme-transition);
}

.mobile-menu.show {
    opacity: 1;
    transform: translateY(0);
}

.mobile-menu-item {
    padding: 10px 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
}

.mobile-menu-item:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.main-content {
    padding: 20px 0;
    min-height: calc(100vh - var(--header-height) - 80px);
}

.view-tabs {
    display: flex;
    gap: 10px;
    padding: 5px;
    margin-bottom: 20px;
}

.tab {
    padding: 6px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-regular);
    transition: var(--theme-transition);
}

.tab:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
}

.tab.active {
    background-color: var(--primary-color);
    color: white;
}

.alerts-panel {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 16px;
    margin-bottom: 20px;
}

.panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.panel-header h3 {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
}

.badge-count {
    background-color: var(--danger-color);
    color: white;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 10px;
}

.alerts-list {
    list-style: none;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px;
    border-radius: var(--border-radius);
    margin-bottom: 8px;
    background-color: var(--bg-color);
    transition: var(--theme-transition);
}

.alert-item:hover {
    background-color: var(--primary-light);
}

.alert-item i {
    font-size: 18px;
}

.alert-item.critical i {
    color: var(--danger-color);
}

.alert-item.warning i {
    color: var(--warning-color);
}

.alert-item.info i {
    color: var(--info-color);
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 500;
    margin-bottom: 3px;
    color: var(--text-primary);
}

.alert-time {
    font-size: 12px;
    color: var(--text-secondary);
}

.hidden {
    display: none;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 16px;
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
    transition: var(--hover-transition);
    cursor: pointer;
    border: 2px solid transparent;
    transform: translateY(0);
}

.stat-card:hover:not(.active) {
    transform: translateY(-3px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-card.active {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-card.active .stat-value,
.stat-card.active .stat-label {
    color: var(--primary-color);
}

.stat-card.active .stat-icon {
    opacity: 0.5;
}

.stat-card:active {
    transform: translateY(0);
}

.stat-value {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-primary);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 14px;
}

.stat-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 36px;
    opacity: 0.2;
}

.stat-icon.online {
    color: var(--success-color);
}

.stat-icon.offline {
    color: var(--secondary-color);
}

.stat-icon.progress {
    color: var(--primary-color);
}

.stat-icon.warning {
    color: var(--warning-color);
}

.stat-icon.error {
    color: var(--error-color);
}

.workstations-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
}

.view-controls {
    display: flex;
    gap: 12px;
}

.search-box {
    display: flex;
    align-items: center;
    background-color: var(--bg-color);
    border-radius: 20px;
    padding: 5px 12px;
}

.search-box i {
    margin-right: 5px;
    color: var(--text-secondary);
}

.search-box input {
    border: none;
    background: transparent;
    outline: none;
    color: var(--text-primary);
    flex: 1;
    min-width: 0;
}

.filter-button {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 12px;
    border-radius: 20px;
    background-color: var(--primary-light);
    color: var(--primary-color);
    cursor: pointer;
}

.workstation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
    align-items: start;
}

.workstation-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: transform 0.15s ease, box-shadow 0.15s ease;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    height: auto;
    min-height: 95px;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    transform-origin: center;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
}

.workstation-card:hover {
    transform: scale(1.03);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    z-index: 10;
}

.card-header {
    padding: 10px 15px;
    color: white;
    flex-shrink: 0;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
}

.card-header h3 {
    font-size: 15px;
    font-weight: 600;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 60%;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
}

.header-top-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 500;
    flex-shrink: 0;
}

.card-body {
    padding: 10px 15px 12px 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 0;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
}

.latest-status {
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 0;
}

.latest-status-message {
    font-size: 10px;
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.4;
    padding: 4px 8px;
    border-radius: 6px;
    background-color: rgba(0, 0, 0, 0.02);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.latest-status-message.online {
    background-color: rgba(76, 175, 80, 0.1);
}

.latest-status-message.offline {
    background-color: rgba(144, 147, 153, 0.1);
}

.latest-status-message.error {
    background-color: rgba(229, 57, 53, 0.1);
}

.tips-line {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.2;
    padding: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-shrink: 0;
    cursor: default;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
}

.progress-status-line {
    font-size: 11px;
    font-weight: 500;
    color: var(--text-regular);
    line-height: 1.2;
    padding: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
}

.progress-status-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    min-width: 0;
}

.version-badge-small {
    font-size: 9px;
    font-weight: 600;
    color: var(--text-secondary);
    background-color: var(--bg-secondary);
    padding: 2px 6px;
    border-radius: 8px;
    margin-left: 8px;
    flex-shrink: 0;
    white-space: nowrap;
    border: 1px solid var(--border-color);
}

.progress-status-line.online {
    color: var(--success-color);
}

.progress-status-line.offline {
    color: var(--secondary-color);
}

.progress-status-line.error {
    color: var(--danger-color);
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2px;
    font-size: 9px;
    color: var(--text-secondary);
    flex-shrink: 0;
}

.last-update {
    font-size: 10px;
}

.view-details {
    color: var(--primary-color);
    font-weight: 500;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal:not(.hidden) {
    opacity: 1;
    visibility: visible;
}

.modal.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 650px;
    max-height: 85vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--border-color);
    overflow: hidden;
    z-index: 1001;
    transform: scale(0.9) translateY(-20px);
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal:not(.hidden) .modal-content {
    transform: scale(1) translateY(0);
}

.modal-header {
    padding: 24px 28px 20px 28px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    background-color: var(--card-bg);
}

.modal-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 22px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 6px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
    transform: scale(1.1);
}

.modal-body {
    padding: 24px 28px 28px 28px;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
    background-color: var(--card-bg);
}

.modal-status-header {
    background: linear-gradient(135deg, var(--status-color), var(--status-color-light, var(--status-color)));
    color: white;
    padding: 16px 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.modal-status-header h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.modal-status-header .status-indicator {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
}

.location-section {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 15px;
}

.info-section {
    margin-bottom: 15px;
}

.info-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.log-count {
    font-size: 12px;
    color: var(--text-tertiary);
    font-weight: normal;
}

.logs-loading {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 14px;
    padding: 10px 0;
}

.logs-loading i {
    color: var(--primary-color);
}

.logs-error {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--danger-color);
    font-size: 14px;
    padding: 10px 0;
}

.logs-pagination {
    text-align: center;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid var(--border-color);
}

.load-more-btn {
    background: linear-gradient(135deg, var(--primary-color), #4a90e2);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.load-more-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-hover), #357abd);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.load-more-btn:disabled {
    background: var(--text-tertiary);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.logs-end {
    text-align: center;
    color: var(--success-color);
    font-size: 12px;
    font-weight: 500;
    padding: 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    background-color: var(--bg-secondary);
    border-radius: 15px;
    margin: 0 20px;
}

.info-value {
    color: var(--text-primary);
}

.status-list {
    list-style: none;
    padding: 0;
    margin: 8px 0 0 0;
}

.status-item {
    display: flex;
    padding: 6px 0;
    border-bottom: 1px solid var(--border-color);
    font-size: 13px;
}

.status-timestamp {
    min-width: 140px;
    color: var(--text-secondary);
}

.status-message {
    flex: 1;
}

.status-message.online {
    color: var(--success-color);
}

.status-message.offline {
    color: var(--secondary-color);
}

.status-message.error {
    color: var(--danger-color);
}

.connection-info {
    margin-top: auto;
    background-color: var(--bg-color);
    border-radius: var(--border-radius);
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.connection-details {
    font-size: 13px;
}

.connection-details .small-text {
    font-size: 11px;
    color: var(--text-secondary);
}

.version-badge {
    background-color: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
}

.app-footer {
    background-color: var(--card-bg);
    padding: 16px 0;
    border-top: 1px solid var(--border-color);
}

.footer-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.copyright {
    color: var(--text-secondary);
    font-size: 12px;
}

.about-links {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
}

.divider {
    color: var(--text-secondary);
}

.footer-stats {
    display: flex;
    gap: 15px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.stat-item .stat-label {
    font-size: 11px;
    color: var(--text-secondary);
}

.stat-item .stat-value {
    font-size: 12px;
    color: var(--text-primary);
    font-weight: 500;
    margin: 0;
}

.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transform: translateY(10px);
    transition: var(--theme-transition);
    z-index: 99;
}

.back-to-top.show {
    opacity: 1;
    transform: translateY(0);
}

.back-to-top i {
    font-size: 24px;
}

@media (max-width:768px) {
    .time-display {
        display: none;
    }

    .mobile-menu-btn {
        display: flex;
    }

    .refresh-btn,
    .theme-btn {
        display: none;
    }

    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .view-controls {
        width: 100%;
    }

    .search-box {
        flex: 1;
    }

    .workstation-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;
    }

    .workstation-card {
        height: auto;
        min-height: 95px;
    }

    .card-header {
        padding: 8px 12px;
    }

    .card-header h3 {
        font-size: 14px;
    }

    .card-body {
        padding: 8px 12px 10px 12px;
    }

    .latest-status-message {
        font-size: 11px;
    }

    .card-footer {
        margin-top: 2px;
        font-size: 9px;
    }

    .tips-line {
        font-size: 11px;
        padding: 0;
    }

    .progress-status-line {
        font-size: 10px;
        padding: 0;
    }

    .version-badge-small {
        font-size: 8px;
        padding: 1px 4px;
        margin-left: 6px;
    }

    .modal-content {
        width: 95%;
        max-width: 100%;
        max-height: 90vh;
        margin: 0 10px;
    }

    .modal-header {
        padding: 20px 24px 16px 24px;
    }

    .modal-header h3 {
        font-size: 18px;
    }

    .modal-body {
        padding: 20px 24px 24px 24px;
    }

    .modal-status-header {
        padding: 14px 18px;
        margin-bottom: 18px;
    }

    .modal-status-header h3 {
        font-size: 16px;
    }

    .modal-status-header .status-indicator {
        padding: 4px 10px;
        font-size: 12px;
    }

    .footer-container {
        flex-direction: column;
        gap: 15px;
    }

    .footer-info {
        text-align: center;
    }

    .about-links {
        justify-content: center;
    }

    .footer-stats {
        width: 100%;
        justify-content: space-around;
    }

    .stat-item {
        align-items: center;
    }
}

@media (max-width:480px) {
    .summary-stats {
        grid-template-columns: 1fr;
    }

    .workstation-grid {
        grid-template-columns: 1fr;
    }

    .workstation-card {
        height: auto;
        min-height: 85px;
    }

    .card-header h3 {
        font-size: 13px;
    }

    .latest-status-message {
        font-size: 10px;
    }

    .modal-content {
        width: 95%;
        max-width: 100%;
        max-height: 90vh;
        margin: 10px;
        border-radius: var(--border-radius);
    }

    .modal-header {
        padding: 16px 20px;
    }

    .modal-header h3 {
        font-size: 16px;
    }

    .modal-body {
        padding: 16px 20px;
    }

    .footer-stats {
        flex-direction: column;
        gap: 8px;
    }

    .stat-item {
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
    }

    .tips-line {
        font-size: 10px;
        padding: 0;
    }

    .progress-status-line {
        font-size: 9px;
        padding: 0;
    }

    .version-badge-small {
        font-size: 7px;
        padding: 1px 3px;
        margin-left: 4px;
    }
}

.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    gap: 15px;
    color: var(--text-secondary);
}

.loading-indicator.hidden {
    display: none;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--primary-light);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.rotating {
    animation: spin 1s linear infinite;
}

.alerts-panel {
    position: sticky;
    top: 60px;
    z-index: 1000;
}