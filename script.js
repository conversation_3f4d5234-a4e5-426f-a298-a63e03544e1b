let allWorkstations = [];
let lastUpdateTime = null;
let currentFilter = null;

function filterWorkstations(status) {
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.classList.remove('active')
    });
    if (currentFilter === status) {
        currentFilter = null
    } else {
        currentFilter = status;
        const selectedCard = document.querySelector(`.stat-card[onclick="filterWorkstations('${status}')"]`);
        selectedCard.classList.add('active')
    }
    renderWorkstations(allWorkstations)
}

function updateCurrentTime() {
    const now = new Date();
    const formattedTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }).replace(/\//g, '-');
    const timeDisplay = document.getElementById('current-time');
    const mobileTimeDisplay = document.getElementById('mobile-time');
    if (timeDisplay) timeDisplay.textContent = formattedTime;
    if (mobileTimeDisplay) mobileTimeDisplay.textContent = formattedTime
}
async function fetchWorkstations(showLoading = true) {
    const loadingIndicator = document.getElementById('loading-indicator');
    const workstationContainer = document.getElementById('workstation-container');
    if (showLoading) {
        loadingIndicator.classList.remove('hidden');
        workstationContainer.innerHTML = ''
    }
    try {
        const response = await fetch('http://192.168.1.19:3000/api/workstations');
        // const response = await fetch('/api/workstations');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }
        const fetchedData = await response.json();
        lastUpdateTime = new Date();
        console.log('Fetched workstations (showLoading:', showLoading, '):', fetchedData);
        if (showLoading) {
            await new Promise(resolve => setTimeout(resolve, 500))
        }
        allWorkstations = fetchedData;
        updateSummaryStats(allWorkstations);
        return allWorkstations
    } catch (error) {
        console.error("Could not fetch workstations:", error);
        if (showLoading) {
            workstationContainer.innerHTML = '<p style="text-align: center; color: var(--danger-color);">加载数据失败。</p>'
        }
        return null
    } finally {
        if (showLoading) {
            loadingIndicator.classList.add('hidden')
        }
    }
}

function updateSummaryStats(workstations) {
    const onlineCount = workstations.filter(ws => ws.overallStatusType === 'online').length;
    const offlineCount = workstations.filter(ws => ws.overallStatusType === 'offline').length;
    const errorCount = workstations.filter(ws => ws.overallStatusType === 'error').length;
    const statCards = document.querySelectorAll('.summary-stats .stat-card');
    if (statCards.length >= 3) {
        statCards[0].querySelector('.stat-value').textContent = onlineCount;
        statCards[1].querySelector('.stat-value').textContent = offlineCount;
        statCards[2].querySelector('.stat-value').textContent = errorCount
    }
}

function sortWorkstations(workstations) {
    return workstations.sort((a, b) => {
        const statusPriority = {
            'error': 3,
            'online': 2,
            'offline': 1
        };
        const aStatus = a.overallStatusType || 'offline';
        const bStatus = b.overallStatusType || 'offline';
        const statusDiff = (statusPriority[bStatus] || 1) - (statusPriority[aStatus] || 1);
        if (statusDiff !== 0) {
            return statusDiff
        }
        return a.name.localeCompare(b.name, 'zh-CN', {
            numeric: true,
            sensitivity: 'base'
        })
    })
}

function renderWorkstations(dataToRender) {
    const container = document.getElementById('workstation-container');
    container.innerHTML = '';
    let filteredWorkstations = currentFilter ? dataToRender.filter(workstation => workstation.overallStatusType === currentFilter) : dataToRender;
    filteredWorkstations = sortWorkstations(filteredWorkstations);
    if (filteredWorkstations.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: var(--text-secondary);">没有找到匹配的工位。</p>';
        return
    }
    filteredWorkstations.forEach(workstation => {
        const statusClass = workstation.overallStatusType || 'offline';
        const statusText = statusClass === 'online' ? '在线' : (statusClass === 'error' ? '异常' : '离线');
        const statusColor = statusClass === 'online' ? 'var(--success-color)' : (statusClass === 'error' ? 'var(--danger-color)' : 'var(--secondary-color)');
        const statusIconClass = statusClass === 'online' ? 'ri-check-line' : (statusClass === 'offline' ? 'ri-close-line' : 'ri-error-warning-line');
        const tipsText = workstation.tips || '无项目描述';
        const progressStatusText = `${workstation.realtimeStatusMessage || '无状态'} - ${workstation.progress || '无进度'}`;
        const versionText = workstation.softwareVersion || 'N/A';
        const card = document.createElement('div');
        card.className = 'workstation-card';
        card.setAttribute('data-workstation-id', workstation.id || workstation.name);
        card.innerHTML = `<div class="card-header" style="background-color: ${statusColor}"><div class="header-top-row"><h3 title="${workstation.name}">${workstation.name}</h3><div class="status-indicator ${statusClass}"><i class="${statusIconClass}"></i><span>${statusText}</span></div></div></div><div class="card-body"><div class="latest-status"><div class="tips-line" title="${tipsText}">${tipsText}</div><div class="progress-status-line ${statusClass}"><span class="progress-status-text">${progressStatusText}</span><span class="version-badge-small">${versionText}</span></div></div></div>`;
        card.addEventListener('click', () => {
            showWorkstationDetails(workstation)
        });
        container.appendChild(card)
    })
}

async function showWorkstationDetails(workstation) {
    const modal = document.getElementById('workstation-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalBody = document.getElementById('modal-body');
    modalTitle.textContent = '工位详情';
    const statusClass = workstation.overallStatusType || 'offline';
    const statusText = statusClass === 'online' ? '在线' : (statusClass === 'error' ? '异常' : '离线');
    const statusColor = statusClass === 'online' ? 'var(--success-color)' : (statusClass === 'error' ? 'var(--danger-color)' : 'var(--secondary-color)');
    const statusIconClass = statusClass === 'online' ? 'ri-check-line' : (statusClass === 'offline' ? 'ri-close-line' : 'ri-error-warning-line');
    const location = workstation.location || 'N/A';

    // 先显示基本信息和加载状态
    modalBody.innerHTML = `<div class="modal-status-header" style="--status-color: ${statusColor}; background-color: ${statusColor};"><h3>${workstation.name}</h3><div class="status-indicator ${statusClass}"><i class="${statusIconClass}"></i><span>${statusText}</span></div></div><div class="card-body"><div class="info-section location-section"><div class="info-label">工位信息：</div><div class="info-value">${location}</div></div><div class="info-section status-log-section"><div class="info-label">运行日志：</div><div class="logs-loading"><i class="ri-loader-4-line rotating"></i><span>正在加载日志数据...</span></div></div><div class="connection-info"><div class="connection-details"><div class="small-text">远程信息</div><div>${workstation.remoteId || 'N/A'}</div></div><span class="version-badge">${workstation.softwareVersion || 'N/A'}</span></div></div>`;

    // 显示模态框
    const hasScrollbar = document.documentElement.scrollHeight > document.documentElement.clientHeight;
    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
    if (hasScrollbar) {
        document.body.style.overflow = 'hidden';
        if (scrollbarWidth > 0) {
            document.body.style.paddingRight = `${scrollbarWidth}px`
        }
    }
    modal.classList.remove('hidden');
    modal.offsetHeight;

    // 异步加载详细日志数据（分页加载）
    try {
        const initialLimit = 5; // 首次只加载5条，便于测试
        const apiUrl = `http://192.168.1.19:3000/api/workstations/${encodeURIComponent(workstation.name)}/logs?limit=${initialLimit}`;
        console.log('Fetching logs from:', apiUrl); // 调试日志
        const response = await fetch(apiUrl);
        console.log('Response status:', response.status); // 调试日志
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }
        const logData = await response.json();
        console.log('Log data received:', logData); // 调试日志
        const statusLogs = logData.logs || [];
        const isFewItems = statusLogs.length <= 3;

        // 更新日志部分，添加分页控制
        const logSection = modalBody.querySelector('.status-log-section');
        if (logSection) {
            const hasMoreLogs = logData.totalLogs > initialLimit;
            console.log(`Has more logs: ${hasMoreLogs}, totalLogs: ${logData.totalLogs}, initialLimit: ${initialLimit}`); // 调试日志
            logSection.innerHTML = `
                <div class="info-label">运行日志：<span class="log-count">(显示${statusLogs.length}/${logData.totalLogs}条)</span></div>
                <ul class="status-list ${isFewItems ? 'few-items' : ''}" id="logs-list">
                    ${statusLogs.length > 0 ? statusLogs.map(log => `<li class="status-item"><div class="status-timestamp">${log.timestamp || 'N/A'}</div><div class="status-message ${log.type || 'unknown'}">${log.message || '无日志信息'}</div></li>`).join('') : '<li class="status-item"><div class="status-message unknown">无运行日志</div></li>'}
                </ul>
                ${hasMoreLogs ? `<div class="logs-pagination">
                    <button class="load-more-btn" onclick="loadMoreLogs('${workstation.name}', ${initialLimit})">
                        <i class="ri-add-line"></i>加载更多日志 (还有${logData.totalLogs - initialLimit}条)
                    </button>
                </div>` : ''}
            `;
        }
    } catch (error) {
        console.error('Failed to load detailed logs:', error);
        // 加载失败时显示错误信息
        const logSection = modalBody.querySelector('.status-log-section');
        if (logSection) {
            logSection.innerHTML = `<div class="info-label">运行日志：</div><div class="logs-error"><i class="ri-error-warning-line"></i><span>日志加载失败，请稍后重试</span></div>`;
        }
    }
}

// 加载更多日志的函数
async function loadMoreLogs(workstationName, currentCount) {
    const loadMoreBtn = document.querySelector('.load-more-btn');
    const logsList = document.getElementById('logs-list');
    const logCountSpan = document.querySelector('.log-count');

    if (!loadMoreBtn || !logsList) return;

    // 显示加载状态
    const originalContent = loadMoreBtn.innerHTML;
    loadMoreBtn.innerHTML = '<i class="ri-loader-4-line rotating"></i>加载中...';
    loadMoreBtn.disabled = true;

    try {
        const nextBatch = 50; // 每次加载50条
        const response = await fetch(`http://192.168.1.19:3000/api/workstations/${encodeURIComponent(workstationName)}/logs?limit=${nextBatch}&offset=${currentCount}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const logData = await response.json();
        const newLogs = logData.logs || [];

        // 添加新的日志项
        if (newLogs.length > 0) {
            const newLogItems = newLogs.map(log =>
                `<li class="status-item"><div class="status-timestamp">${log.timestamp || 'N/A'}</div><div class="status-message ${log.type || 'unknown'}">${log.message || '无日志信息'}</div></li>`
            ).join('');
            logsList.insertAdjacentHTML('beforeend', newLogItems);

            // 更新计数
            const newCurrentCount = currentCount + newLogs.length;
            if (logCountSpan) {
                logCountSpan.textContent = `(显示${newCurrentCount}/${logData.totalLogs}条)`;
            }

            // 检查是否还有更多日志
            const remainingLogs = logData.totalLogs - newCurrentCount;
            if (remainingLogs > 0) {
                loadMoreBtn.innerHTML = `<i class="ri-add-line"></i>加载更多日志 (还有${remainingLogs}条)`;
                loadMoreBtn.disabled = false;
                loadMoreBtn.setAttribute('onclick', `loadMoreLogs('${workstationName}', ${newCurrentCount})`);
            } else {
                // 没有更多日志了
                loadMoreBtn.parentElement.innerHTML = '<div class="logs-end"><i class="ri-check-line"></i>已显示全部日志</div>';
            }
        } else {
            // 没有更多日志
            loadMoreBtn.parentElement.innerHTML = '<div class="logs-end"><i class="ri-check-line"></i>已显示全部日志</div>';
        }

    } catch (error) {
        console.error('Failed to load more logs:', error);
        loadMoreBtn.innerHTML = '<i class="ri-error-warning-line"></i>加载失败，点击重试';
        loadMoreBtn.disabled = false;
    }
}

function closeWorkstationModal() {
    const modal = document.getElementById('workstation-modal');
    if (modal && !modal.classList.contains('hidden')) {
        modal.classList.add('hidden');
        document.body.style.overflow = '';
        document.body.style.paddingRight = ''
    }
}

function handleScroll() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const header = document.getElementById('app-header');
    const backToTop = document.getElementById('back-to-top');
    if (scrollTop > 10) {
        header.classList.add('scrolled')
    } else {
        header.classList.remove('scrolled')
    }
    if (scrollTop > 300) {
        backToTop.classList.add('show')
    } else {
        backToTop.classList.remove('show')
    }
}

function toggleTheme() {
    const html = document.documentElement;
    const themeIcon = document.querySelector('#theme-toggle i');
    const mobileThemeIcon = document.querySelector('#mobile-theme-toggle i');
    const mobileThemeText = document.querySelector('#mobile-theme-toggle span');
    if (html.classList.contains('dark')) {
        html.classList.remove('dark');
        html.classList.add('light');
        themeIcon.className = 'ri-sun-line';
        mobileThemeIcon.className = 'ri-sun-line';
        mobileThemeText.textContent = '切换暗色模式';
        localStorage.setItem('theme', 'light')
    } else {
        html.classList.remove('light');
        html.classList.add('dark');
        themeIcon.className = 'ri-moon-line';
        mobileThemeIcon.className = 'ri-moon-line';
        mobileThemeText.textContent = '切换亮色模式';
        localStorage.setItem('theme', 'dark')
    }
}

function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    mobileMenu.classList.toggle('show');
    if (mobileMenu.classList.contains('show')) {
        mobileMenu.style.display = 'block'
    } else {
        setTimeout(() => {
            mobileMenu.style.display = 'none'
        }, 300)
    }
}
async function refreshData() {
    const refreshBtn = document.querySelector('.refresh-btn i');
    refreshBtn.classList.remove('ri-refresh-line');
    refreshBtn.classList.add('ri-loader-4-line', 'rotating');
    const freshData = await fetchWorkstations(true);
    if (freshData) {
        renderWorkstations(freshData)
    }
    setTimeout(() => {
        refreshBtn.classList.remove('ri-loader-4-line', 'rotating');
        refreshBtn.classList.add('ri-refresh-line')
    }, 100)
}
async function autoRefreshData() {
    console.log("Auto refreshing data...");
    const freshData = await fetchWorkstations(false);
    if (freshData) {
        renderWorkstations(freshData)
    } else {
        console.log("Auto refresh failed or returned no data.")
    }
}
document.getElementById('search-input').addEventListener('input', function () {
    const searchTerm = this.value.toLowerCase();
    const cards = document.querySelectorAll('.workstation-card');
    cards.forEach(card => {
        const cardText = card.textContent.toLowerCase();
        if (cardText.includes(searchTerm)) {
            card.style.display = ''
        } else {
            card.style.display = 'none'
        }
    })
});
document.getElementById('mobile-menu-toggle').addEventListener('click', toggleMobileMenu);
const mobileMenuItems = document.querySelectorAll('.mobile-menu-item');
mobileMenuItems.forEach(item => {
    item.addEventListener('click', function () {
        toggleMobileMenu()
    })
});
window.addEventListener('scroll', handleScroll);
document.getElementById('back-to-top').addEventListener('click', function () {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    })
});
document.getElementById('theme-toggle').addEventListener('click', toggleTheme);
document.getElementById('mobile-theme-toggle').addEventListener('click', toggleTheme);

function initTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    const html = document.documentElement;
    const themeIcon = document.querySelector('#theme-toggle i');
    const mobileThemeIcon = document.querySelector('#mobile-theme-toggle i');
    const mobileThemeText = document.querySelector('#mobile-theme-toggle span');
    if (savedTheme === 'dark') {
        html.classList.add('dark');
        html.classList.remove('light');
        themeIcon.className = 'ri-moon-line';
        mobileThemeIcon.className = 'ri-moon-line';
        mobileThemeText.textContent = '切换亮色模式'
    } else {
        html.classList.add('light');
        html.classList.remove('dark');
        themeIcon.className = 'ri-sun-line';
        mobileThemeIcon.className = 'ri-sun-line';
        mobileThemeText.textContent = '切换暗色模式'
    }
}
document.head.insertAdjacentHTML('beforeend', `<style>@keyframes rotating{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}.rotating{animation:rotating 1s linear infinite}</style>`);
document.addEventListener('DOMContentLoaded', async function () {
    initTheme();
    updateCurrentTime();
    setInterval(updateCurrentTime, 500);
    const initialData = await fetchWorkstations(true);
    if (initialData) {
        renderWorkstations(initialData)
    }
    handleScroll();
    setInterval(autoRefreshData, 30000);
    const modal = document.getElementById('workstation-modal');
    const modalClose = modal.querySelector('.modal-close');
    const modalOverlay = modal.querySelector('.modal-overlay');
    modalClose.addEventListener('click', closeWorkstationModal);
    modalOverlay.addEventListener('click', closeWorkstationModal);
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
            closeWorkstationModal()
        }
    })
})